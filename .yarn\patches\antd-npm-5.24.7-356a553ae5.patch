diff --git a/es/dropdown/dropdown.js b/es/dropdown/dropdown.js
index 986877a762b9ad0aca596a8552732cd12d2eaabb..1f18aa2ea745e68950e4cee16d4d655f5c835fd5 100644
--- a/es/dropdown/dropdown.js
+++ b/es/dropdown/dropdown.js
@@ -2,7 +2,7 @@
 
 import * as React from 'react';
 import LeftOutlined from "@ant-design/icons/es/icons/LeftOutlined";
-import RightOutlined from "@ant-design/icons/es/icons/RightOutlined";
+import { ChevronRight } from 'lucide-react';
 import classNames from 'classnames';
 import RcDropdown from 'rc-dropdown';
 import useEvent from "rc-util/es/hooks/useEvent";
@@ -158,8 +158,10 @@ const Dropdown = props => {
         className: `${prefixCls}-menu-submenu-arrow`
       }, direction === 'rtl' ? (/*#__PURE__*/React.createElement(LeftOutlined, {
         className: `${prefixCls}-menu-submenu-arrow-icon`
-      })) : (/*#__PURE__*/React.createElement(RightOutlined, {
-        className: `${prefixCls}-menu-submenu-arrow-icon`
+      })) : (/*#__PURE__*/React.createElement(ChevronRight, {
+        size: 16,
+        strokeWidth: 1.8,
+        className: `${prefixCls}-menu-submenu-arrow-icon lucide-custom`
       }))),
       mode: "vertical",
       selectable: false,
diff --git a/es/dropdown/style/index.js b/es/dropdown/style/index.js
index 768c01783002c6901c85a73061ff6b3e776a60ce..39b1b95a56cdc9fb586a193c3adad5141f5cf213 100644
--- a/es/dropdown/style/index.js
+++ b/es/dropdown/style/index.js
@@ -240,7 +240,8 @@ const genBaseStyle = token => {
               marginInlineEnd: '0 !important',
               color: token.colorTextDescription,
               fontSize: fontSizeIcon,
-              fontStyle: 'normal'
+              fontStyle: 'normal',
+              marginTop: 3,
             }
           }
         }),
diff --git a/es/select/useIcons.js b/es/select/useIcons.js
index 959115be936ef8901548af2658c5dcfdc5852723..c812edd52123eb0faf4638b1154fcfa1b05b513b 100644
--- a/es/select/useIcons.js
+++ b/es/select/useIcons.js
@@ -4,10 +4,10 @@ import * as React from 'react';
 import CheckOutlined from "@ant-design/icons/es/icons/CheckOutlined";
 import CloseCircleFilled from "@ant-design/icons/es/icons/CloseCircleFilled";
 import CloseOutlined from "@ant-design/icons/es/icons/CloseOutlined";
-import DownOutlined from "@ant-design/icons/es/icons/DownOutlined";
 import LoadingOutlined from "@ant-design/icons/es/icons/LoadingOutlined";
 import SearchOutlined from "@ant-design/icons/es/icons/SearchOutlined";
 import { devUseWarning } from '../_util/warning';
+import { ChevronDown } from 'lucide-react';
 export default function useIcons(_ref) {
   let {
     suffixIcon,
@@ -56,8 +56,10 @@ export default function useIcons(_ref) {
           className: iconCls
         }));
       }
-      return getSuffixIconNode(/*#__PURE__*/React.createElement(DownOutlined, {
-        className: iconCls
+      return getSuffixIconNode(/*#__PURE__*/React.createElement(ChevronDown, {
+        size: 16,
+        strokeWidth: 1.8,
+        className: `${iconCls} lucide-custom`
       }));
     };
   }
