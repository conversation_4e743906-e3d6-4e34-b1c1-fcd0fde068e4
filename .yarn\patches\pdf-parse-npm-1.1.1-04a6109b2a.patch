diff --git a/lib/pdf-parse.js b/lib/pdf-parse.js
index 96bfbc705dcb4fb73cb077a75f02c115371b3477..6d02d2bb426063c3a31cb740c3d86841de162a22 100644
--- a/lib/pdf-parse.js
+++ b/lib/pdf-parse.js
@@ -21,12 +21,12 @@ function render_page(pageData) {
             for (let item of textContent.items) {
                 if (lastY == item.transform[5] || !lastY){
                     text += item.str;
-                }  
+                }
                 else{
                     text += '\n' + item.str;
-                }    
+                }
                 lastY = item.transform[5];
-            }            
+            }
             //let strings = textContent.items.map(item => item.str);
             //let text = strings.join("\n");
             //text = text.replace(/[ ]+/ig," ");
@@ -60,7 +60,7 @@ async function PDF(dataBuffer, options) {
     if (typeof options.version != 'string') options.version = DEFAULT_OPTIONS.version;
     if (options.version == 'default') options.version = DEFAULT_OPTIONS.version;
 
-    PDFJS = PDFJS ? PDFJS : require(`./pdf.js/${options.version}/build/pdf.js`);
+    PDFJS = PDFJS ? PDFJS : require(`./pdf.js/v1.10.100/build/pdf.js`);
 
     ret.version = PDFJS.version;
 
