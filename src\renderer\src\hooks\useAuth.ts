import NavigationService from '@renderer/services/NavigationService'
import store, { useAppSelector } from '@renderer/store'
import { setAccessToken, setIsLogin, setUser } from '@renderer/store/auth'

export const useAuth = () => {
  const { user, username, accessToken, serverUrl, isLogin } = useAppSelector((state) => state.auth)

  return { user, username, accessToken, serverUrl, isLogin }
}

export const logout = () => {
  store.dispatch(setUser(undefined))
  store.dispatch(setAccessToken(undefined))
  store.dispatch(setIsLogin(false))
  NavigationService.navigate?.('/')
}
