appId: com.cherry-ai.cherry-stuido-enterprise
productName: Cherry Studio 企业版
electronLanguages:
  - zh-CN
  - zh-TW
  - en-US
  - ja # macOS/linux/win
  - ru # macOS/linux/win
  - zh_CN # for macOS
  - zh_TW # for macOS
  - en # for macOS
directories:
  buildResources: build

protocols:
  - name: Cherry Studio
    schemes:
      - cherrystudio
files:
  - '**/*'
  - '!**/{.vscode,.yarn,.yarn-lock,.github,.cursorrules,.prettierrc}'
  - '!electron.vite.config.{js,ts,mjs,cjs}}'
  - '!**/{.eslintignore,.eslintrc.js,.eslintrc.json,.eslintcache,root.eslint.config.js,eslint.config.js,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,eslint.config.mjs,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!**/{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!**/{tsconfig.json,tsconfig.tsbuildinfo,tsconfig.node.json,tsconfig.web.json}'
  - '!**/{.editorconfig,.jekyll-metadata}'
  - '!src'
  - '!scripts'
  - '!local'
  - '!docs'
  - '!packages'
  - '!.swc'
  - '!.bin'
  - '!._*'
  - '!*.log'
  - '!stats.html'
  - '!*.md'
  - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
  - '!**/*.{map,ts,tsx,jsx,less,scss,sass,css.d.ts,d.cts,d.mts,md,markdown,yaml,yml}'
  - '!**/{test,tests,__tests__,powered-test,coverage}/**'
  - '!**/{example,examples}/**'
  - '!**/*.{spec,test}.{js,jsx,ts,tsx}'
  - '!**/*.min.*.map'
  - '!**/*.d.ts'
  - '!**/dist/es6/**'
  - '!**/dist/demo/**'
  - '!**/amd/**'
  - '!**/{.DS_Store,Thumbs.db,thumbs.db,__pycache__}'
  - '!**/{LICENSE,license,LICENSE.*,*.LICENSE.txt,NOTICE.txt,README.md,readme.md,CHANGELOG.md}'
  - '!node_modules/rollup-plugin-visualizer'
  - '!node_modules/js-tiktoken'
  - '!node_modules/@tavily/core/node_modules/js-tiktoken'
  - '!node_modules/pdf-parse/lib/pdf.js/{v1.9.426,v1.10.88,v2.0.550}'
  - '!node_modules/mammoth/{mammoth.browser.js,mammoth.browser.min.js}'
  - '!node_modules/selection-hook/prebuilds/**/*' # we rebuild .node, don't use prebuilds
  - '!node_modules/pdfjs-dist/web/**/*'
  - '!node_modules/pdfjs-dist/legacy/web/*'
  - '!node_modules/selection-hook/node_modules' # we don't need what in the node_modules dir
  - '!node_modules/selection-hook/src' # we don't need source files
  - '!**/*.{h,iobj,ipdb,tlog,recipe,vcxproj,vcxproj.filters,Makefile,*.Makefile}' # filter .node build files
asarUnpack:
  - resources/**
  - '**/*.{metal,exp,lib}'
win:
  executableName: Cherry Studio 企业版
  artifactName: Cherry-Studio-Enterprise-${version}-${arch}-setup.${ext}
  target:
    - target: nsis
    # - target: portable
  signtoolOptions:
    sign: scripts/win-sign.js
  verifyUpdateCodeSignature: false
nsis:
  artifactName: Cherry-Studio-Enterprise-${version}-${arch}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  allowToChangeInstallationDirectory: true
  oneClick: false
  include: build/nsis-installer.nsh
  buildUniversalInstaller: false
mac:
  entitlementsInherit: build/entitlements.mac.plist
  notarize: false
  artifactName: Cherry-Studio-Enterprise-${version}-${arch}.${ext}
  minimumSystemVersion: '20.1.0' # 最低支持 macOS 11.0
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  target:
    - target: dmg
    - target: zip
linux:
  artifactName: Cherry-Studio-Enterprise-${version}-${arch}.${ext}
  target:
    - target: AppImage
    - target: deb
  maintainer: electronjs.org
  category: Utility
  desktop:
    entry:
      StartupWMClass: CherryStudio
  mimeTypes:
    - x-scheme-handler/cherrystudio
publish:
  provider: generic
  url: https://releases.enterprise.cherry-ai.com
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
afterPack: scripts/after-pack.js
afterSign: scripts/notarize.js
artifactBuildCompleted: scripts/artifact-build-completed.js
releaseInfo:
  releaseNotes: |
    • [新增] MCP 工具调用自动审批流程
    • [优化] 输入框快捷弹窗多选交互支持
    • [新增] 网页内容生成实时预览功能
    • [支持] Grok-4 大语言模型接入
    • [修复] Anthropic 模型输出截断缺陷
