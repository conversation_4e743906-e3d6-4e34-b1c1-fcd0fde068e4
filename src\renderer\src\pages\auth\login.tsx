import { LinkOutlined, LockOutlined, UserOutlined } from '@ant-design/icons'
import api, { updateApiBasePath, updateApiToken } from '@renderer/config/api'
import { APP_NAME, AppLogo } from '@renderer/config/env'
import { useAuth } from '@renderer/hooks/useAuth'
import { syncConfig } from '@renderer/services/sync/sync'
import { useAppDispatch } from '@renderer/store'
import { setAccessToken, setIsLogin, setServerUrl, setUser, setUsername } from '@renderer/store/auth'
import { setUserName } from '@renderer/store/settings'
import { isAdmin } from '@renderer/utils/auth'
import { IpcChannel } from '@shared/IpcChannel'
import { Button, Card, Divider, Form, Input, notification as Notification } from 'antd'
import { AxiosError } from 'axios'
import { FC, useEffect, useState } from 'react'
import styled from 'styled-components'

interface LoginFormValues {
  username: string
  password: string
  serverUrl: string
}

const LoginPage: FC = () => {
  const [loading, setLoading] = useState(false)
  const [ssoLoading, setSsoLoading] = useState(false)
  const [currentServerUrl, setCurrentServerUrl] = useState('')
  const dispatch = useAppDispatch()
  const { serverUrl, username } = useAuth()
  const [notification, contextHolder] = Notification.useNotification()
  const [form] = Form.useForm()

  // 验证URL是否合法
  const isValidUrl = (url: string): boolean => {
    try {
      if (!url?.trim()) return false
      const urlObj = new URL(url.trim())
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch (error) {
      return false
    }
  }

  // 获取当前表单中的服务器URL
  const getCurrentServerUrl = (): string => {
    return form.getFieldValue('serverUrl') || ''
  }

  // 检查SSO按钮是否应该禁用
  const isSSOButtonDisabled = (): boolean => {
    return !isValidUrl(currentServerUrl)
  }

  // 监听表单值变化
  useEffect(() => {
    const serverUrlValue = getCurrentServerUrl()
    setCurrentServerUrl(serverUrlValue)
  }, [form.getFieldValue('serverUrl')])

  // 监听表单字段变化
  const handleFormValuesChange = (changedValues: any) => {
    if (changedValues.serverUrl !== undefined) {
      setCurrentServerUrl(changedValues.serverUrl || '')
    }
  }

  // 清理SSO回调处理器
  useEffect(() => {
    const cleanup = window.electron.ipcRenderer.on(IpcChannel.OAuth_Casdoor, async (_, data) => {
      console.log('OAuth_Casdoor', data)

      if (data?.token && data?.user) {
        const { user, token } = data

        if (isAdmin(user)) {
          notification.error({ message: '管理员禁止登录客户端', duration: 5 })
          setSsoLoading(false)
          return
        }

        updateApiToken(token)

        dispatch(setUser(user))
        dispatch(setUsername(user.username))
        dispatch(setAccessToken(token))
        dispatch(setUserName(user.username))

        // 同步配置
        try {
          await syncConfig({ syncAll: true })
          dispatch(setIsLogin(true))
        } catch (error: any) {
          console.error('[SSO Login] syncConfig error', error)
          notification.error({ message: '同步配置失败', description: getMessage(error), duration: 5 })
          setSsoLoading(false)
        }

        setSsoLoading(false)
      } else {
        // 如果没有收到有效数据，也要重置loading状态
        setSsoLoading(false)
      }
    })

    return () => {
      cleanup()
      // 组件卸载时重置loading状态
      setSsoLoading(false)
    }
  }, [dispatch, notification])

  // 组件卸载时清理loading状态
  useEffect(() => {
    return () => {
      setSsoLoading(false)
    }
  }, [])

  const validateUrl = (_: any, value: string) => {
    try {
      if (!value) {
        return Promise.reject('请输入服务端地址')
      }
      const url = new URL(value.trim())
      if (!['http:', 'https:'].includes(url.protocol)) {
        return Promise.reject('请输入有效的 HTTP 或 HTTPS URL')
      }
      return Promise.resolve()
    } catch (error) {
      return Promise.reject('请输入有效的 URL 地址')
    }
  }

  const handleSSOLogin = async () => {
    // 如果已经在loading状态，不允许重复点击
    if (ssoLoading) {
      return
    }

    try {
      // 使用当前状态中的服务器URL
      const serverUrlValue = currentServerUrl

      if (!serverUrlValue?.trim()) {
        notification.error({ message: '请先输入服务端地址', duration: 5 })
        return
      }

      if (!isValidUrl(serverUrlValue)) {
        notification.error({ message: '请输入有效的服务端地址', duration: 5 })
        return
      }

      const serverUrl = serverUrlValue.trim().replace(/\/+$/, '')
      const ssoUrl = `${serverUrl}/auth/casdoor?client=desktop`

      // 设置服务端地址
      dispatch(setServerUrl(serverUrl))

      // 使用默认浏览器打开SSO登录页面
      window.api.openWebsite(ssoUrl)

      setSsoLoading(true)

      // 设置超时
      setTimeout(() => {
        setSsoLoading((prevLoading) => {
          if (prevLoading) {
            notification.error({ message: 'SSO登录超时，请重试', duration: 5 })
            return false
          }
          return prevLoading
        })
      }, 300000) // 5分钟超时
    } catch (error: any) {
      console.error('[SSO Login] error', error)
      notification.error({ message: 'SSO登录失败', description: getMessage(error), duration: 5 })
      setSsoLoading(false)
    }
  }

  const onFinish = async (values: LoginFormValues) => {
    setLoading(true)

    try {
      const serverUrl = values.serverUrl.trim().replace(/\/+$/, '')

      // 设置服务端地址
      dispatch(setServerUrl(serverUrl))

      // 设置 API 基路径
      updateApiBasePath(serverUrl)

      const { data } = await api.authLogin({
        authLoginRequest: {
          username: values.username,
          password: values.password
        }
      })

      const { user, access_token } = data

      if (isAdmin(user)) {
        notification.error({ message: '管理员禁止登录客户端', duration: 5 })
        return
      }

      updateApiToken(access_token)

      if (user && access_token) {
        dispatch(setUser(user))
        dispatch(setUsername(user.username))
        dispatch(setAccessToken(access_token))
        dispatch(setUserName(user.username))
        access_token && updateApiToken(access_token)
        serverUrl && updateApiBasePath(serverUrl)
      }

      try {
        await syncConfig({ syncAll: true })
      } catch (error: any) {
        console.error('[Login] syncConfig error', error)
        notification.error({ message: '同步配置失败', description: getMessage(error), duration: 5 })
        return
      }

      dispatch(setIsLogin(true))
    } catch (error: any) {
      console.error('[Login] error', error)
      notification.error({ message: '登录失败', description: getMessage(error), duration: 5 })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container>
      <TitleBar />
      {contextHolder}
      <LoginCard>
        <LogoContainer>
          <img src={AppLogo} alt="App Logo" />
          <BrandTitle>{APP_NAME}</BrandTitle>
        </LogoContainer>
        <Form
          form={form}
          name="login"
          initialValues={{ remember: true, serverUrl, username }}
          onFinish={onFinish}
          size="large"
          onValuesChange={handleFormValuesChange}>
          <Form.Item name="serverUrl" rules={[{ validator: validateUrl }]}>
            <Input prefix={<LinkOutlined />} placeholder="服务端地址" />
          </Form.Item>

          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { pattern: /^\S+$/, message: '用户名不能包含空格' }
            ]}>
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { pattern: /^\S+$/, message: '密码不能包含空格' }
            ]}>
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <LoginButton type="primary" htmlType="submit" block loading={loading}>
              登录
            </LoginButton>
          </Form.Item>
        </Form>

        <Divider plain style={{ margin: '16px 0' }}>
          或
        </Divider>

        <SSOButton type="default" block loading={ssoLoading} onClick={handleSSOLogin} disabled={isSSOButtonDisabled()}>
          SSO 登录
        </SSOButton>
      </LoginCard>
    </Container>
  )
}

const getMessage = (error: any) => {
  if (error instanceof AxiosError) {
    if (error.response?.status === 401) {
      return '用户名或密码错误'
    }
    return error.response?.data.message
  }
  return error.message
}

const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  background-color: transparent;
  -webkit-app-region: no-drag;
  position: relative;
`

const TitleBar = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: transparent;
  -webkit-app-region: drag;
  z-index: 1000;
`

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 25px;
  padding: 15px 24px;
  -webkit-app-region: no-drag;
`

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 24px;

  img {
    height: 64px;
    width: auto;
    border-radius: 50%;
    margin-bottom: 12px;
  }
`

const BrandTitle = styled.h1`
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  padding: 0;
`

const LoginButton = styled(Button)`
  height: 40px;
`

const SSOButton = styled(Button)`
  height: 40px;
`

export default LoginPage
