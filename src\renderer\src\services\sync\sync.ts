import { Agent } from '@cherrystudio/api-sdk'
import api, { updateApiBasePath, updateApiToken } from '@renderer/config/api'
import store from '@renderer/store'
import { setFlows } from '@renderer/store/flow'

import {
  syncAgents,
  syncAssistantModels,
  syncDefaultModels,
  syncKnowledgeBases,
  syncMcpServers,
  syncMinapps,
  syncProvider,
  syncSettings,
  syncWebSearchProviders,
  validateServerConfig
} from '.'

export async function syncConfig({ syncAll = false }: { syncAll?: boolean } = {}): Promise<void> {
  const { accessToken, serverUrl } = store.getState().auth
  const dispatch = store.dispatch

  if (!accessToken || !serverUrl) {
    return
  }

  // 更新 API 配置
  updateApiToken(accessToken)
  updateApiBasePath(serverUrl)

  // 获取服务器配置
  const { data: configurations } = await api.configurationGetConfigurations()
  validateServerConfig(configurations)

  // 同步服务商
  syncProvider(configurations.providers)

  // 同步默认模型
  syncDefaultModels(configurations)

  // 同步助手
  syncAssistantModels(configurations.models)

  // 同步网络搜索提供商
  syncWebSearchProviders(configurations.settings)

  // 同步智能体
  syncAgents(configurations.agents as Agent[])

  // 同步知识库
  syncKnowledgeBases(configurations.knowledgeBases)

  // 同步 MCP 服务器
  syncAll && syncMcpServers(configurations.mcpServers)

  // 同步设置
  syncSettings(configurations.settings)

  // 同步小程序
  if (configurations.minApps) {
    syncMinapps(configurations.minApps)
  }

  // 同步工作流
  if (configurations.workflows) {
    dispatch(setFlows(configurations.workflows))
  }
}
