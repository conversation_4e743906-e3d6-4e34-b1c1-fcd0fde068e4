diff --git a/epub.js b/epub.js
index 50efff7678ca4879ed639d3bb70fd37e7477fd16..accbe689cd200bd59475dd20fca596511d0f33e0 100644
--- a/epub.js
+++ b/epub.js
@@ -3,9 +3,28 @@ var xml2jsOptions = xml2js.defaults['0.1'];
 var EventEmitter = require('events').EventEmitter;
 
 try {
-    // zipfile is an optional dependency:
-    var ZipFile = require("zipfile").ZipFile;
-} catch (err) {
+    var zipread = require("zipread");
+    var ZipFile = function(filename) {
+        var zip = zipread(filename);
+        this.zip = zip;
+        var files = zip.files;
+
+        files = Object.values(files).filter((file) => {
+            return !file.dir;
+        }).map((file) => {
+            return file.name;
+        });
+
+        this.names = files;
+        this.count = this.names.length;
+    };
+    ZipFile.prototype.readFile = function(name, cb) {
+        this.zip.readFile(name
+            , function(err, buffer) {
+                return cb(null, buffer);
+            });
+    };
+} catch(err) {
     // Mock zipfile using pure-JS adm-zip:
     var AdmZip = require('adm-zip');
 
diff --git a/package.json b/package.json
index 8c3dccf0caac8913a2edabd7049b25bb9063c905..57bac3b71ddd73916adbdf00b049089181db5bcb 100644
--- a/package.json
+++ b/package.json
@@ -40,10 +40,8 @@
   ],
   "dependencies": {
     "adm-zip": "^0.4.11",
-    "xml2js": "^0.4.23"
-  },
-  "optionalDependencies": {
-    "zipfile": "^0.5.11"
+    "xml2js": "^0.4.23",
+    "zipread": "^1.3.3"
   },
   "devDependencies": {
     "@types/mocha": "^5.2.5",
